"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/hero/AnimatedHero.tsx":
/*!**********************************************!*\
  !*** ./src/components/hero/AnimatedHero.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedHero: function() { return /* binding */ AnimatedHero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(app-pages-browser)/../../node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CurrencyDollarIcon,FireIcon,SparklesIcon,StarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/../../node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ AnimatedHero auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n// Floating elements data\nconst floatingElements = [\n    {\n        id: 1,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        delay: 0,\n        x: \"10%\",\n        y: \"20%\",\n        duration: 3\n    },\n    {\n        id: 2,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        delay: 0.5,\n        x: \"80%\",\n        y: \"30%\",\n        duration: 4\n    },\n    {\n        id: 3,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        delay: 1,\n        x: \"15%\",\n        y: \"70%\",\n        duration: 3.5\n    },\n    {\n        id: 4,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        delay: 1.5,\n        x: \"85%\",\n        y: \"60%\",\n        duration: 2.8\n    },\n    {\n        id: 5,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        delay: 2,\n        x: \"50%\",\n        y: \"15%\",\n        duration: 3.2\n    },\n    {\n        id: 6,\n        icon: _barrel_optimize_names_BoltIcon_CurrencyDollarIcon_FireIcon_SparklesIcon_StarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        delay: 2.5,\n        x: \"70%\",\n        y: \"80%\",\n        duration: 3.8\n    }\n];\n// Live odds ticker data\nconst liveOdds = [\n    {\n        team1: \"Lakers\",\n        team2: \"Warriors\",\n        odds: \"2.45\",\n        change: \"+0.15\"\n    },\n    {\n        team1: \"Chelsea\",\n        team2: \"Arsenal\",\n        odds: \"1.85\",\n        change: \"-0.05\"\n    },\n    {\n        team1: \"Cowboys\",\n        team2: \"Giants\",\n        odds: \"3.20\",\n        change: \"+0.25\"\n    },\n    {\n        team1: \"Real Madrid\",\n        team2: \"Barcelona\",\n        odds: \"2.10\",\n        change: \"+0.10\"\n    },\n    {\n        team1: \"Celtics\",\n        team2: \"Heat\",\n        odds: \"1.95\",\n        change: \"-0.15\"\n    }\n];\n// Animated counter component\nfunction AnimatedCounter(param) {\n    let { end, duration = 2, prefix = \"\", suffix = \"\" } = param;\n    _s();\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let startTime;\n        let animationFrame;\n        const animate = (timestamp)=>{\n            if (!startTime) startTime = timestamp;\n            const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);\n            setCount(Math.floor(progress * end));\n            if (progress < 1) {\n                animationFrame = requestAnimationFrame(animate);\n            }\n        };\n        animationFrame = requestAnimationFrame(animate);\n        return ()=>cancelAnimationFrame(animationFrame);\n    }, [\n        end,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: [\n            prefix,\n            count.toLocaleString(),\n            suffix\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n        lineNumber: 61,\n        columnNumber: 10\n    }, this);\n}\n_s(AnimatedCounter, \"/xL7qdScToREtqzbt5GZ1kHtYjQ=\");\n_c = AnimatedCounter;\n// Typewriter effect component\nfunction TypewriterText(param) {\n    let { text, delay = 0 } = param;\n    _s1();\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            if (currentIndex < text.length) {\n                setDisplayText((prev)=>prev + text[currentIndex]);\n                setCurrentIndex((prev)=>prev + 1);\n            }\n        }, delay + currentIndex * 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        currentIndex,\n        text,\n        delay\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        children: [\n            displayText,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.span, {\n                animate: {\n                    opacity: [\n                        1,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 0.8,\n                    repeat: Infinity,\n                    repeatType: \"reverse\"\n                },\n                className: \"inline-block w-1 h-8 bg-yellow-400 ml-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s1(TypewriterText, \"hIUgadE6edYynGHHGmHPDKhBDus=\");\n_c1 = TypewriterText;\nfunction AnimatedHero() {\n    _s2();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary-600 via-primary-700 to-secondary-600 p-8 text-white h-96\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-black/20\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl lg:text-6xl font-bold mb-4\",\n                            children: [\n                                \"Welcome to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400\",\n                                    children: \"Kesar Mango\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 24\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl lg:text-2xl mb-6 opacity-90\",\n                            children: \"Experience the ultimate betting platform with live odds and instant payouts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        className: \"relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary-600 via-primary-700 to-secondary-600 p-6 sm:p-8 lg:p-12 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                animate: {\n                    background: [\n                        \"linear-gradient(45deg, #667eea, #764ba2)\",\n                        \"linear-gradient(45deg, #764ba2, #667eea)\",\n                        \"linear-gradient(45deg, #667eea, #764ba2)\"\n                    ]\n                },\n                transition: {\n                    duration: 8,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: floatingElements.map((element)=>{\n                    const IconComponent = element.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0\n                        },\n                        animate: {\n                            opacity: [\n                                0,\n                                1,\n                                1,\n                                0\n                            ],\n                            scale: [\n                                0,\n                                1,\n                                1,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -20,\n                                0,\n                                -20\n                            ],\n                            rotate: [\n                                0,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: element.duration,\n                            delay: element.delay,\n                            repeat: Infinity,\n                            repeatDelay: 2,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute\",\n                        style: {\n                            left: element.x,\n                            top: element.y\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                            className: \"h-6 w-6 sm:h-8 sm:w-8 text-yellow-400/60\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, this)\n                    }, element.id, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: Array.from({\n                    length: 20\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"absolute w-1 h-1 bg-yellow-400/30 rounded-full\",\n                        initial: {\n                            x: Math.random() * 100 + \"%\",\n                            y: \"100%\",\n                            opacity: 0\n                        },\n                        animate: {\n                            y: \"-10%\",\n                            opacity: [\n                                0,\n                                1,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: Math.random() * 3 + 2,\n                            delay: Math.random() * 5,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        }\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col items-center text-center lg:items-start lg:text-left\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h1, {\n                        className: \"text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold mb-4 lg:mb-6 leading-tight\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        children: [\n                            \"Welcome to \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-yellow-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypewriterText, {\n                                    text: \"Kesar Mango\",\n                                    delay: 1000\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 22\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                        className: \"text-lg sm:text-xl lg:text-2xl mb-6 lg:mb-8 opacity-90 max-w-3x leading-relaxed\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        children: \"Experience the ultimate betting platform with live odds and instant payouts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 mb-8 w-full max-w-4xl\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl lg:text-3xl font-bold text-yellow-400 leading-tight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                            end: 125847,\n                                            prefix: \"\",\n                                            suffix: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm lg:text-base opacity-80 mt-1\",\n                                        children: \"Active Users\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl lg:text-3xl font-bold text-green-400 leading-tight\",\n                                        children: [\n                                            \"$\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                end: 45,\n                                                prefix: \"\",\n                                                suffix: \".9M\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 16\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm lg:text-base opacity-80 mt-1\",\n                                        children: \"Total Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl lg:text-3xl font-bold text-blue-400 leading-tight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                            end: 2,\n                                            prefix: \"\",\n                                            suffix: \".8M\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm lg:text-base opacity-80 mt-1\",\n                                        children: \"Total Bets\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl lg:text-3xl font-bold text-purple-400 leading-tight\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                            end: 156,\n                                            prefix: \"\",\n                                            suffix: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm lg:text-base opacity-80 mt-1\",\n                                        children: \"Live Events\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: \"0 0 30px rgba(255, 255, 255, 0.3)\"\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-white text-primary-700 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 relative overflow-hidden group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300\",\n                                        initial: false\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: \"Start Betting Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05,\n                                    boxShadow: \"0 0 30px rgba(255, 255, 255, 0.2)\"\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold hover:bg-white hover:text-primary-700 transition-all duration-300 relative overflow-hidden group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        className: \"absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                        initial: false\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: \"Explore Casino\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                className: \"absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-sm border-t border-white/10 p-2 overflow-hidden\",\n                initial: {\n                    y: 100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"flex space-x-8 whitespace-nowrap\",\n                    animate: {\n                        x: [\n                            0,\n                            -100 * liveOdds.length\n                        ]\n                    },\n                    transition: {\n                        duration: 20,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                    },\n                    children: [\n                        ...liveOdds,\n                        ...liveOdds\n                    ].map((odd, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white\",\n                                    children: [\n                                        odd.team1,\n                                        \" vs \",\n                                        odd.team2\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-400 font-bold\",\n                                    children: odd.odds\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs \".concat(odd.change.startsWith(\"+\") ? \"text-green-400\" : \"text-red-400\"),\n                                    children: odd.change\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s2(AnimatedHero, \"LrrVfNW3d1raFE0BNzCTILYmIfo=\");\n_c2 = AnimatedHero;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedCounter\");\n$RefreshReg$(_c1, \"TypewriterText\");\n$RefreshReg$(_c2, \"AnimatedHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hero/AnimatedHero.tsx\n"));

/***/ })

});