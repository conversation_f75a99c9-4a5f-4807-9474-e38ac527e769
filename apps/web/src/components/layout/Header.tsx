'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  BellIcon,
  UserCircleIcon,
  MagnifyingGlassIcon,
  WalletIcon,
} from '@heroicons/react/24/outline'
import { useBetting } from '@/store/betting-store'

interface HeaderProps {
  onMenuClick?: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const { selectionCount, toggleSlip } = useBetting()
  
  // Mock user data
  const user = {
    username: 'BetMaster',
    balance: 1250.50,
    currency: 'USD',
    avatar: null,
  }

  return (
    <header className="sticky top-0 z-50 bg-dark-900/95 backdrop-blur-md border-b border-dark-700 w-full">
      <div className="w-full px-4 lg:px-6 xl:px-8 2xl:px-12">
        <div className="flex items-center justify-between h-16 w-full">
          {/* Left section */}
          <div className="flex items-center space-x-4">
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2"
            >
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">K</span>
              </div>
              <span className="hidden sm:block text-xl font-bold text-white">
                Kesar Mango
              </span>
            </motion.div>
          </div>

          {/* Center section - Search */}
          <div className="hidden md:flex flex-1 max-w-sm lg:max-w-md xl:max-w-lg 2xl:max-w-xl mx-4 lg:mx-8">
            <div className="relative w-full">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search events, teams, or games..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-4">
            {/* Wallet Balance */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="hidden sm:flex items-center space-x-2 bg-dark-800 px-4 py-2 rounded-lg border border-dark-600"
            >
              <WalletIcon className="h-5 w-5 text-primary-400" />
              <div className="text-right">
                <div className="text-sm text-gray-400">Balance</div>
                <div className="font-semibold text-white">
                  ${user.balance.toFixed(2)}
                </div>
              </div>
            </motion.div>

            {/* Betting Slip Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleSlip}
              className="relative bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <span className="hidden sm:inline">Bet Slip</span>
              <span className="sm:hidden">Slip</span>
              {selectionCount > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-2 -right-2 bg-secondary-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold"
                >
                  {selectionCount}
                </motion.span>
              )}
            </motion.button>

            {/* Notifications */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative p-2 rounded-lg hover:bg-dark-700 transition-colors"
            >
              <BellIcon className="h-6 w-6 text-gray-300" />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </motion.button>

            {/* User Menu */}
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-dark-700 transition-colors"
              >
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.username}
                    className="h-8 w-8 rounded-full"
                  />
                ) : (
                  <UserCircleIcon className="h-8 w-8 text-gray-300" />
                )}
                <span className="hidden lg:block text-white font-medium">
                  {user.username}
                </span>
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile search */}
      <div className="md:hidden px-4 pb-4">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search events, teams, or games..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>
    </header>
  )
}
